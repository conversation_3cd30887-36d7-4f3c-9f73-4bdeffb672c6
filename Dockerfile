FROM node:20-alpine AS builder

# Set the working directory
WORKDIR /app

# Copia arquivos de dependência
COPY package*.json ./

# Instala dependências
RUN npm install -g npm && npm install


# Copia código fonte
COPY . .

# Gera o cliente Prisma
RUN npx prisma generate

# Stage development: Prepare the runtime image
FROM node:22-alpine AS development

# Set the working directory
WORKDIR /app

# Copy the built application from the builder stage
COPY --from=builder /app /app

# Run the application using pnpm
CMD ["sh", "-c", "npm run start"] 