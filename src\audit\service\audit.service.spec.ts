import { Test, TestingModule } from '@nestjs/testing';
import { AuditService } from './audit.service';
import { AuditRepository } from '../repository/audit.repository.interface';
import { AuditMessage } from '../domain/audit-message';

const mockAuditRepository = {
  create: jest.fn(),
  findAll: jest.fn(),
};

describe('AuditService', () => {
  let service: AuditService;
  let auditRepository: AuditRepository;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AuditService,
        {
          provide: 'AuditRepository',
          useValue: mockAuditRepository,
        },
      ],
    }).compile();

    service = module.get<AuditService>(AuditService);
    auditRepository = module.get<AuditRepository>('AuditRepository');
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should call auditRepository.create with the correct audit message', async () => {
      // Arrange
      const auditMessage = new AuditMessage(
        'USER_LOGIN',
        '2025-06-26T12:00:00.000Z',
        'testuser',
        'POST',
        '/api/login',
        { email: '<EMAIL>' },
        '***********',
        'Mozilla/5.0 Test Agent',
      );

      mockAuditRepository.create.mockResolvedValue(undefined);

      // Act
      await service.create(auditMessage);

      // Assert
      // eslint-disable-next-line @typescript-eslint/unbound-method
      expect(auditRepository.create).toHaveBeenCalledWith(auditMessage);
      // eslint-disable-next-line @typescript-eslint/unbound-method
      expect(auditRepository.create).toHaveBeenCalledTimes(1);
    });

    it('should throw an error if repository fails', async () => {
      // Arrange
      const auditMessage = new AuditMessage(
        'USER_LOGIN',
        '2025-06-26T12:00:00.000Z',
        'testuser',
        'POST',
        '/api/login',
        { email: '<EMAIL>' },
        '***********',
        'Mozilla/5.0 Test Agent',
      );

      const errorMessage = 'Database connection failed';
      mockAuditRepository.create.mockRejectedValue(new Error(errorMessage));

      // Act & Assert
      await expect(service.create(auditMessage)).rejects.toThrow(errorMessage);
      // eslint-disable-next-line @typescript-eslint/unbound-method
      expect(auditRepository.create).toHaveBeenCalledWith(auditMessage);
    });
  });

  describe('findAll', () => {
    const mockAuditMessage = new AuditMessage(
      'USER_LOGIN',
      '2025-06-29T10:00:00.000Z',
      'testuser',
      'POST',
      '/api/login',
      { email: '<EMAIL>' },
      '***********',
      'Mozilla/5.0',
    );

    const mockFindAllResult = {
      data: [mockAuditMessage],
      total: 1,
      page: 1,
      limit: 10,
      totalPages: 1,
    };

    it('should call auditRepository.findAll with correct parameters', async () => {
      // Arrange
      mockAuditRepository.findAll.mockResolvedValue(mockFindAllResult);

      // Act
      const result = await service.findAll('testuser', '2025-06-29', 1, 10);

      // Assert
      // eslint-disable-next-line @typescript-eslint/unbound-method
      expect(auditRepository.findAll).toHaveBeenCalledWith({
        username: 'testuser',
        timestamp: '2025-06-29',
        page: 1,
        limit: 10,
        offset: 0, // (page - 1) * limit
      });
      expect(result).toEqual(mockFindAllResult);
    });

    it('should calculate offset correctly for different pages', async () => {
      // Arrange
      mockAuditRepository.findAll.mockResolvedValue(mockFindAllResult);

      // Act
      await service.findAll('testuser', '2025-06-29', 3, 15);

      // Assert
      // eslint-disable-next-line @typescript-eslint/unbound-method
      expect(auditRepository.findAll).toHaveBeenCalledWith({
        username: 'testuser',
        timestamp: '2025-06-29',
        page: 3,
        limit: 15,
        offset: 30, // (3 - 1) * 15
      });
    });

    it('should handle optional parameters', async () => {
      // Arrange
      mockAuditRepository.findAll.mockResolvedValue(mockFindAllResult);

      // Act
      await service.findAll(undefined, undefined, 1, 10);

      // Assert
      // eslint-disable-next-line @typescript-eslint/unbound-method
      expect(auditRepository.findAll).toHaveBeenCalledWith({
        username: undefined,
        timestamp: undefined,
        page: 1,
        limit: 10,
        offset: 0,
      });
    });

    it('should use default values for page and limit', async () => {
      // Arrange
      mockAuditRepository.findAll.mockResolvedValue(mockFindAllResult);

      // Act
      await service.findAll('testuser', '2025-06-29');

      // Assert
      // eslint-disable-next-line @typescript-eslint/unbound-method
      expect(auditRepository.findAll).toHaveBeenCalledWith({
        username: 'testuser',
        timestamp: '2025-06-29',
        page: 1,
        limit: 10,
        offset: 0,
      });
    });

    it('should enforce maximum limit of 50', async () => {
      // Arrange
      mockAuditRepository.findAll.mockResolvedValue(mockFindAllResult);

      // Act
      await service.findAll('testuser', '2025-06-29', 1, 100);

      // Assert
      // eslint-disable-next-line @typescript-eslint/unbound-method
      expect(auditRepository.findAll).toHaveBeenCalledWith({
        username: 'testuser',
        timestamp: '2025-06-29',
        page: 1,
        limit: 50, // Should be capped at 50
        offset: 0,
      });
    });

    it('should propagate repository errors', async () => {
      // Arrange
      const error = new Error('Repository error');
      mockAuditRepository.findAll.mockRejectedValue(error);

      // Act & Assert
      await expect(
        service.findAll('testuser', '2025-06-29', 1, 10),
      ).rejects.toThrow('Repository error');
    });

    it('should handle empty results', async () => {
      // Arrange
      const emptyResult = {
        data: [],
        total: 0,
        page: 1,
        limit: 10,
        totalPages: 0,
      };
      mockAuditRepository.findAll.mockResolvedValue(emptyResult);

      // Act
      const result = await service.findAll('nonexistent', '2025-06-29', 1, 10);

      // Assert
      expect(result).toEqual(emptyResult);
      expect(result.data).toHaveLength(0);
    });
  });
});
