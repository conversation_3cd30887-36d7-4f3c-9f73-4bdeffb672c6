import { FindAllAuditMessagesResponseDto } from './find-all-audit-messages-response-dto';
import { AuditMessage } from '../domain/audit-message';

describe('FindAllAuditMessagesResponseDto', () => {
  it('should have the correct structure', () => {
    const mockAuditMessage = new AuditMessage(
      'USER_LOGIN',
      '2025-06-29T10:00:00.000Z',
      'testuser',
      'POST',
      '/api/login',
      { email: '<EMAIL>' },
      '192.168.1.1',
      'Mozilla/5.0',
    );

    const responseDto: FindAllAuditMessagesResponseDto = {
      data: [mockAuditMessage],
      total: 1,
      page: 1,
      limit: 10,
      totalPages: 1,
    };

    expect(responseDto.data).toHaveLength(1);
    expect(responseDto.data[0]).toBeInstanceOf(AuditMessage);
    expect(responseDto.total).toBe(1);
    expect(responseDto.page).toBe(1);
    expect(responseDto.limit).toBe(10);
    expect(responseDto.totalPages).toBe(1);
  });

  it('should accept empty data array', () => {
    const emptyResponseDto: FindAllAuditMessagesResponseDto = {
      data: [],
      total: 0,
      page: 1,
      limit: 10,
      totalPages: 0,
    };

    expect(emptyResponseDto.data).toHaveLength(0);
    expect(emptyResponseDto.total).toBe(0);
    expect(emptyResponseDto.totalPages).toBe(0);
  });

  it('should handle large datasets correctly', () => {
    const mockAuditMessage = new AuditMessage(
      'USER_LOGIN',
      '2025-06-29T10:00:00.000Z',
      'testuser',
      'POST',
      '/api/login',
      { email: '<EMAIL>' },
      '192.168.1.1',
      'Mozilla/5.0',
    );

    const largeResponseDto: FindAllAuditMessagesResponseDto = {
      data: Array(50).fill(mockAuditMessage) as AuditMessage[],
      total: 1000,
      page: 5,
      limit: 50,
      totalPages: 20,
    };

    expect(largeResponseDto.data).toHaveLength(50);
    expect(largeResponseDto.total).toBe(1000);
    expect(largeResponseDto.page).toBe(5);
    expect(largeResponseDto.totalPages).toBe(20);
  });
});
