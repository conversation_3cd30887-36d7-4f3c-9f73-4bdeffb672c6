export class AuditMessage {
  eventType: string;
  timestamp: string;
  username: string;
  method: string;
  endpoint: string;
  body: Record<string, unknown>;
  ip: string;
  userAgent: string;

  constructor(
    eventType: string,
    timestamp: string,
    username: string,
    method: string,
    endpoint: string,
    body: Record<string, unknown>,
    ip: string,
    userAgent: string,
  ) {
    this.eventType = eventType;
    this.timestamp = timestamp;
    this.username = username;
    this.method = method;
    this.endpoint = endpoint;
    this.body = body;
    this.ip = ip;
    this.userAgent = userAgent;
    this.validate();
  }
  validate() {
    if (
      !this.eventType ||
      !this.timestamp ||
      !this.username ||
      !this.method ||
      !this.endpoint ||
      !this.ip ||
      !this.userAgent
    ) {
      throw new Error('Invalid AuditMessage: Missing required fields');
    }
    if (
      typeof this.body !== 'object' ||
      this.body === null ||
      Array.isArray(this.body)
    ) {
      throw new Error('Invalid AuditMessage: Body must be an object');
    }
  }
}
