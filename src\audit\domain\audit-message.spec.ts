import { AuditMessage } from './audit-message';

describe('AuditMessage', () => {
  const validParams = [
    'USER_LOGIN',
    '2025-06-26T12:00:00.000Z',
    'testuser',
    'POST',
    '/api/login',
    { email: '<EMAIL>' },
    '***********',
    'Mozilla/5.0 Test Agent',
  ] as const;

  it('should create a valid AuditMessage with all required fields', () => {
    // Act
    const auditMessage = new AuditMessage(...validParams);

    // Assert
    expect(auditMessage.eventType).toBe('USER_LOGIN');
    expect(auditMessage.timestamp).toBe('2025-06-26T12:00:00.000Z');
    expect(auditMessage.username).toBe('testuser');
    expect(auditMessage.method).toBe('POST');
    expect(auditMessage.endpoint).toBe('/api/login');
    expect(auditMessage.body).toEqual({ email: '<EMAIL>' });
    expect(auditMessage.ip).toBe('***********');
    expect(auditMessage.userAgent).toBe('Mozilla/5.0 Test Agent');
  });

  describe('validation', () => {
    it('should throw error when eventType is missing', () => {
      expect(() => {
        new AuditMessage(
          '', // Empty eventType
          '2025-06-26T12:00:00.000Z',
          'testuser',
          'POST',
          '/api/login',
          { email: '<EMAIL>' },
          '***********',
          'Mozilla/5.0 Test Agent',
        );
      }).toThrow('Invalid AuditMessage: Missing required fields');
    });

    it('should throw error when timestamp is missing', () => {
      expect(() => {
        new AuditMessage(
          'USER_LOGIN',
          '', // Empty timestamp
          'testuser',
          'POST',
          '/api/login',
          { email: '<EMAIL>' },
          '***********',
          'Mozilla/5.0 Test Agent',
        );
      }).toThrow('Invalid AuditMessage: Missing required fields');
    });

    it('should throw error when username is missing', () => {
      expect(() => {
        new AuditMessage(
          'USER_LOGIN',
          '2025-06-26T12:00:00.000Z',
          '', // Empty username
          'POST',
          '/api/login',
          { email: '<EMAIL>' },
          '***********',
          'Mozilla/5.0 Test Agent',
        );
      }).toThrow('Invalid AuditMessage: Missing required fields');
    });

    it('should throw error when method is missing', () => {
      expect(() => {
        new AuditMessage(
          'USER_LOGIN',
          '2025-06-26T12:00:00.000Z',
          'testuser',
          '', // Empty method
          '/api/login',
          { email: '<EMAIL>' },
          '***********',
          'Mozilla/5.0 Test Agent',
        );
      }).toThrow('Invalid AuditMessage: Missing required fields');
    });

    it('should throw error when endpoint is missing', () => {
      expect(() => {
        new AuditMessage(
          'USER_LOGIN',
          '2025-06-26T12:00:00.000Z',
          'testuser',
          'POST',
          '', // Empty endpoint
          { email: '<EMAIL>' },
          '***********',
          'Mozilla/5.0 Test Agent',
        );
      }).toThrow('Invalid AuditMessage: Missing required fields');
    });

    it('should throw error when ip is missing', () => {
      expect(() => {
        new AuditMessage(
          'USER_LOGIN',
          '2025-06-26T12:00:00.000Z',
          'testuser',
          'POST',
          '/api/login',
          { email: '<EMAIL>' },
          '', // Empty ip
          'Mozilla/5.0 Test Agent',
        );
      }).toThrow('Invalid AuditMessage: Missing required fields');
    });

    it('should throw error when userAgent is missing', () => {
      expect(() => {
        new AuditMessage(
          'USER_LOGIN',
          '2025-06-26T12:00:00.000Z',
          'testuser',
          'POST',
          '/api/login',
          { email: '<EMAIL>' },
          '***********',
          '', // Empty userAgent
        );
      }).toThrow('Invalid AuditMessage: Missing required fields');
    });

    it('should throw error when body is not an object', () => {
      expect(() => {
        new AuditMessage(
          'USER_LOGIN',
          '2025-06-26T12:00:00.000Z',
          'testuser',
          'POST',
          '/api/login',
          'invalid-body' as unknown as Record<string, unknown>, // Invalid body type
          '***********',
          'Mozilla/5.0 Test Agent',
        );
      }).toThrow('Invalid AuditMessage: Body must be an object');
    });

    it('should throw error when body is null', () => {
      expect(() => {
        new AuditMessage(
          'USER_LOGIN',
          '2025-06-26T12:00:00.000Z',
          'testuser',
          'POST',
          '/api/login',
          null as unknown as Record<string, unknown>, // Null body
          '***********',
          'Mozilla/5.0 Test Agent',
        );
      }).toThrow('Invalid AuditMessage: Body must be an object');
    });

    it('should throw error when body is an array', () => {
      expect(() => {
        new AuditMessage(
          'USER_LOGIN',
          '2025-06-26T12:00:00.000Z',
          'testuser',
          'POST',
          '/api/login',
          [] as unknown as Record<string, unknown>, // Array body
          '***********',
          'Mozilla/5.0 Test Agent',
        );
      }).toThrow('Invalid AuditMessage: Body must be an object');
    });

    it('should accept empty object as body', () => {
      expect(() => {
        new AuditMessage(
          'HEALTH_CHECK',
          '2025-06-26T12:00:00.000Z',
          'system',
          'GET',
          '/health',
          {}, // Empty object body
          '127.0.0.1',
          'Health Monitor/1.0',
        );
      }).not.toThrow();
    });

    it('should accept complex nested object as body', () => {
      const complexBody = {
        user: {
          id: 123,
          profile: {
            name: 'Test User',
            settings: {
              theme: 'dark',
              notifications: true,
            },
          },
        },
        metadata: {
          source: 'web',
          version: '1.0.0',
        },
      };

      expect(() => {
        new AuditMessage(
          'USER_UPDATE',
          '2025-06-26T12:00:00.000Z',
          'testuser',
          'PUT',
          '/api/users/123',
          complexBody,
          '***********',
          'Mozilla/5.0 Test Agent',
        );
      }).not.toThrow();
    });
  });

  describe('edge cases', () => {
    it('should handle special characters in all string fields', () => {
      expect(() => {
        new AuditMessage(
          'USER_ACTION_åäöü',
          '2025-06-26T12:00:00.000Z',
          '<EMAIL>',
          'POST',
          '/api/ação/特殊',
          { message: 'Tésting spécial chärs 测试' },
          '2001:db8::1',
          'Mozilla/5.0 (特殊字符) Test/1.0',
        );
      }).not.toThrow();
    });

    it('should handle IPv6 addresses', () => {
      expect(() => {
        new AuditMessage(
          'USER_LOGIN',
          '2025-06-26T12:00:00.000Z',
          'testuser',
          'POST',
          '/api/login',
          { email: '<EMAIL>' },
          '2001:0db8:85a3:0000:0000:8a2e:0370:7334', // IPv6
          'Mozilla/5.0 Test Agent',
        );
      }).not.toThrow();
    });

    it('should handle very long strings', () => {
      const longString = 'a'.repeat(1000);
      expect(() => {
        new AuditMessage(
          'LONG_EVENT',
          '2025-06-26T12:00:00.000Z',
          longString,
          'POST',
          `/api/long/${longString}`,
          { data: longString },
          '***********',
          `Very Long User Agent ${longString}`,
        );
      }).not.toThrow();
    });
  });
});
