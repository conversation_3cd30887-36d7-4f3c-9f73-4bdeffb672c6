import { Inject, Injectable } from '@nestjs/common';
import { AuditMessage } from '../domain/audit-message';
import {
  AuditRepository,
  FindAllResult,
} from '../repository/audit.repository.interface';

@Injectable()
export class AuditService {
  constructor(
    @Inject('AuditRepository')
    private readonly auditRepository: AuditRepository,
  ) {}

  async create(auditMessage: AuditMessage): Promise<void> {
    await this.auditRepository.create(auditMessage);
  }

  async findAll(
    username?: string,
    timestamp?: string,
    page: number = 1,
    limit: number = 10,
  ): Promise<FindAllResult> {
    const actualLimit = Math.min(limit, 50);
    const offset = (page - 1) * actualLimit;

    return await this.auditRepository.findAll({
      username,
      timestamp,
      page,
      limit: actualLimit,
      offset,
    });
  }
}
