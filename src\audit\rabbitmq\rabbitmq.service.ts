import { RabbitSubscribe } from '@golevelup/nestjs-rabbitmq';
import { Injectable, Logger } from '@nestjs/common';
import { AuditService } from '../service/audit.service';
import { AuditMessage } from '../domain/audit-message';

@Injectable()
export class RabbitmqService {
  private readonly logger = new Logger(RabbitmqService.name);

  constructor(private readonly auditService: AuditService) {}

  @RabbitSubscribe({
    exchange:
      process.env.AUDIT_EXCHANGE_NAME ??
      (() => {
        throw new Error('AUDIT_EXCHANGE_NAME is not defined');
      })(),
    routingKey:
      process.env.AUDIT_ROUTING_KEY ??
      (() => {
        throw new Error('AUDIT_ROUTING_KEY is not defined');
      })(),
    queue:
      process.env.AUDIT_QUEUE_NAME ??
      (() => {
        throw new Error('AUDIT_QUEUE_NAME is not defined');
      })(),
  })
  public async handleMessage(msg: Record<string, unknown>) {
    this.logger.debug(`Mensagem recebida: ${JSON.stringify(msg)}`);

    const auditMessage = new AuditMessage(
      msg.eventType as string,
      msg.timestamp as string,
      msg.username as string,
      msg.method as string,
      msg.endpoint as string,
      msg.body as Record<string, unknown>,
      msg.ip as string,
      msg.userAgent as string,
    );

    await this.auditService.create(auditMessage);
  }
}
