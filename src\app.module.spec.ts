// Set environment variables before any imports
process.env.RABBITMQ_URI = 'amqp://localhost:5672';
process.env.AUDIT_EXCHANGE_NAME = 'test.exchange';
process.env.AUDIT_ROUTING_KEY = 'test.routing.key';
process.env.AUDIT_QUEUE_NAME = 'test.queue';

import { Test, TestingModule } from '@nestjs/testing';
import { AppModule } from './app.module';
import { ConfigModule } from '@nestjs/config';
import { PrismaModule } from './prisma/prisma.module';
import { AuditModule } from './audit/audit.module';

// Mock external dependencies
jest.mock('@golevelup/nestjs-rabbitmq', () => ({
  RabbitMQModule: {
    forRoot: jest.fn().mockReturnValue({
      module: class MockRabbitMQModule {},
      providers: [],
      exports: [],
    }),
  },
  RabbitSubscribe: jest.fn().mockImplementation(() => () => {}),
}));

describe('AppModule', () => {
  let module: TestingModule;

  beforeEach(async () => {
    module = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();
  });

  afterEach(async () => {
    if (module) {
      await module.close();
    }
  });

  it('should be defined', () => {
    expect(module).toBeDefined();
  });

  it('should have ConfigModule imported globally', () => {
    const configModule = module.get(ConfigModule);
    expect(configModule).toBeDefined();
  });

  it('should have PrismaModule imported', () => {
    const prismaModule = module.get(PrismaModule);
    expect(prismaModule).toBeDefined();
  });

  it('should have AuditModule imported', () => {
    const auditModule = module.get(AuditModule);
    expect(auditModule).toBeDefined();
  });

  it('should compile successfully', () => {
    expect(module).toBeInstanceOf(TestingModule);
  });
});
