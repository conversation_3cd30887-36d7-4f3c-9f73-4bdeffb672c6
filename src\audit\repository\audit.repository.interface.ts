import { AuditMessage } from '../domain/audit-message';

export interface FindAllParams {
  username?: string;
  timestamp?: string;
  page: number;
  limit: number;
  offset: number;
}

export interface FindAllResult {
  data: AuditMessage[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

export interface AuditRepository {
  create(auditMessage: AuditMessage): Promise<void>;
  findAll(params: FindAllParams): Promise<FindAllResult>;
}
