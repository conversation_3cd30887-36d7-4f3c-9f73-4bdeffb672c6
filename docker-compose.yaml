services:
  backoffice-audit-service:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - '${PORT:-3001}:3000'
    environment:
      - DATABASE_URL=${DATABASE_URL:-********************************************/auditdb?schema=audit}
      - RABBITMQ_URI=${RABBITMQ_URI:-amqp://guest:guest@rabbitmq:5672}
      - AUDIT_EXCHANGE_NAME=${AUDIT_EXCHANGE_NAME:-audit.events}
      - AUDIT_QUEUE_NAME=${AUDIT_QUEUE_NAME:-audit.log.queue}
      - AUDIT_ROUTING_KEY=${AUDIT_ROUTING_KEY:-audit.action}
    depends_on:
      postgres:
        condition: service_healthy
      rabbitmq-setup:
        condition: service_completed_successfully
    restart: unless-stopped

  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: auditdb
    ports:
      - '5433:5432'
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ['CMD-SHELL', 'pg_isready -U postgres -d auditdb']
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped

  rabbitmq-setup:
    image: node:20-slim
    working_dir: /app
    volumes:
      - .:/app
    environment:
      - RABBITMQ_URI=${RABBITMQ_URI:-amqp://guest:guest@rabbitmq:5672}
      - AUDIT_EXCHANGE_NAME=${AUDIT_EXCHANGE_NAME:-audit.events}
    command: >
      sh -c "
        npm install amqplib && \
        node setup-rabbitmq.js
      "
    depends_on:
      rabbitmq:
        condition: service_healthy

  rabbitmq:
    image: rabbitmq:3-management
    ports:
      - '5672:5672'
      - '15672:15672'
    environment:
      RABBITMQ_DEFAULT_USER: guest
      RABBITMQ_DEFAULT_PASS: guest
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    healthcheck:
      test: ['CMD', 'rabbitmq-diagnostics', '-q', 'check_port_connectivity']
      interval: 10s
      timeout: 10s
      retries: 10
      start_period: 30s
    restart: unless-stopped

volumes:
  postgres_data:
  rabbitmq_data:
