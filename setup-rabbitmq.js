const amqp = require('amqplib');

const rabbitmqUri = process.env.RABBITMQ_URI;
const exchangeName = process.env.AUDIT_EXCHANGE_NAME;

console.log('Environment variables:');
console.log('RABBITMQ_URI:', rabbitmqUri);
console.log('AUDIT_EXCHANGE_NAME:', exchangeName);

if (!rabbitmqUri || !exchangeName) {
  console.error(
    'RABBITMQ_URI and AUDIT_EXCHANGE_NAME environment variables must be set.',
  );
  process.exit(1);
}

const connectWithRetry = async () => {
  for (let i = 0; i < 15; i++) {
    try {
      console.log(`Attempting to connect to RabbitMQ... (${i + 1}/15)`);
      const connection = await amqp.connect(rabbitmqUri);
      console.log('Successfully connected to RabbitMQ.');
      return connection;
    } catch (err) {
      console.log(`RabbitMQ not ready: ${err.message}`);
      console.log(`Retrying in 10 seconds... (${i + 1}/15)`);
      await new Promise((resolve) => setTimeout(resolve, 10000));
    }
  }
  throw new Error('Could not connect to RabbitMQ after 15 retries.');
};

const setup = async () => {
  let connection;
  try {
    connection = await connectWithRetry();
    const channel = await connection.createChannel();
    await channel.assertExchange(exchangeName, 'topic', { durable: true });
    console.log(`Exchange "${exchangeName}" is ready.`);
    await channel.close();
    await connection.close();
    process.exit(0);
  } catch (error) {
    console.error('Error during RabbitMQ setup:', error.message);
    if (connection) {
      await connection.close();
    }
    process.exit(1);
  }
};

setup();
