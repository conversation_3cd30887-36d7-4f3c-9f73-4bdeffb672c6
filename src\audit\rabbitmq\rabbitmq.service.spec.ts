// Set environment variables before importing the service
process.env.AUDIT_EXCHANGE_NAME = 'test.exchange';
process.env.AUDIT_ROUTING_KEY = 'test.routing.key';
process.env.AUDIT_QUEUE_NAME = 'test.queue';

import { Test, TestingModule } from '@nestjs/testing';
import { Logger } from '@nestjs/common';
import { RabbitmqService } from './rabbitmq.service';
import { AuditService } from '../service/audit.service';
import { AuditMessage } from '../domain/audit-message';

const mockAuditService = {
  create: jest.fn(),
};

describe('RabbitmqService', () => {
  let service: RabbitmqService;
  let auditService: AuditService;
  let loggerSpy: jest.SpyInstance;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RabbitmqService,
        {
          provide: AuditService,
          useValue: mockAuditService,
        },
      ],
    }).compile();

    service = module.get<RabbitmqService>(RabbitmqService);
    auditService = module.get<AuditService>(AuditService);

    // Mock Logger
    loggerSpy = jest.spyOn(Logger.prototype, 'debug').mockImplementation();
  });

  afterEach(() => {
    jest.clearAllMocks();
    loggerSpy.mockRestore();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('handleMessage', () => {
    it('should process a valid message and create audit entry', async () => {
      // Arrange
      const mockMessage = {
        eventType: 'USER_LOGIN',
        timestamp: '2025-06-26T12:00:00.000Z',
        username: 'testuser',
        method: 'POST',
        endpoint: '/api/login',
        body: { email: '<EMAIL>' },
        ip: '***********',
        userAgent: 'Mozilla/5.0 Test Agent',
      };

      mockAuditService.create.mockResolvedValue(undefined);

      // Act
      await service.handleMessage(mockMessage);

      // Assert
      expect(loggerSpy).toHaveBeenCalledWith(
        `Mensagem recebida: ${JSON.stringify(mockMessage)}`,
      );
      // eslint-disable-next-line @typescript-eslint/unbound-method
      expect(auditService.create).toHaveBeenCalledWith(
        expect.any(AuditMessage),
      );
      // eslint-disable-next-line @typescript-eslint/unbound-method
      expect(auditService.create).toHaveBeenCalledTimes(1);

      // Verify the AuditMessage was created correctly
      const auditMessageArg = (
        (auditService.create as jest.Mock).mock.calls[0] as [AuditMessage]
      )[0];
      expect(auditMessageArg).toBeInstanceOf(AuditMessage);
      expect(auditMessageArg.eventType).toBe(mockMessage.eventType);
      expect(auditMessageArg.username).toBe(mockMessage.username);
      expect(auditMessageArg.method).toBe(mockMessage.method);
      expect(auditMessageArg.endpoint).toBe(mockMessage.endpoint);
      expect(auditMessageArg.ip).toBe(mockMessage.ip);
      expect(auditMessageArg.userAgent).toBe(mockMessage.userAgent);
    });

    it('should throw an error for invalid message data', async () => {
      // Arrange
      const invalidMessage = {
        eventType: '', // Invalid: empty eventType
        timestamp: '2025-06-26T12:00:00.000Z',
        username: 'testuser',
        method: 'POST',
        endpoint: '/api/login',
        body: { email: '<EMAIL>' },
        ip: '***********',
        userAgent: 'Mozilla/5.0 Test Agent',
      };

      // Act & Assert
      await expect(service.handleMessage(invalidMessage)).rejects.toThrow(
        'Invalid AuditMessage: Missing required fields',
      );
      // eslint-disable-next-line @typescript-eslint/unbound-method
      expect(auditService.create).not.toHaveBeenCalled();
    });

    it('should throw an error for message with invalid body type', async () => {
      // Arrange
      const invalidMessage = {
        eventType: 'USER_LOGIN',
        timestamp: '2025-06-26T12:00:00.000Z',
        username: 'testuser',
        method: 'POST',
        endpoint: '/api/login',
        body: 'invalid-body-type', // Invalid: should be object
        ip: '***********',
        userAgent: 'Mozilla/5.0 Test Agent',
      };

      // Act & Assert
      await expect(service.handleMessage(invalidMessage)).rejects.toThrow(
        'Invalid AuditMessage: Body must be an object',
      );
      // eslint-disable-next-line @typescript-eslint/unbound-method
      expect(auditService.create).not.toHaveBeenCalled();
    });

    it('should propagate audit service errors', async () => {
      // Arrange
      const mockMessage = {
        eventType: 'USER_LOGIN',
        timestamp: '2025-06-26T12:00:00.000Z',
        username: 'testuser',
        method: 'POST',
        endpoint: '/api/login',
        body: { email: '<EMAIL>' },
        ip: '***********',
        userAgent: 'Mozilla/5.0 Test Agent',
      };

      const errorMessage = 'Audit service failed';
      mockAuditService.create.mockRejectedValue(new Error(errorMessage));

      // Act & Assert
      await expect(service.handleMessage(mockMessage)).rejects.toThrow(
        errorMessage,
      );
      // eslint-disable-next-line @typescript-eslint/unbound-method
      expect(auditService.create).toHaveBeenCalledWith(
        expect.any(AuditMessage),
      );
    });
  });

  describe('error handling and edge cases', () => {
    it('should handle message with null values gracefully', async () => {
      // Arrange
      const messageWithNulls = {
        eventType: 'USER_LOGIN',
        timestamp: '2025-06-26T12:00:00.000Z',
        username: 'testuser',
        method: 'POST',
        endpoint: '/api/login',
        body: { field: null },
        ip: null, // This will be converted to string "null"
        userAgent: null, // This will be converted to string "null"
      };

      mockAuditService.create.mockResolvedValue(undefined);

      // Act & Assert
      // This should throw because ip and userAgent as null strings will fail validation
      await expect(service.handleMessage(messageWithNulls)).rejects.toThrow();
    });

    it('should handle message with undefined values', async () => {
      // Arrange
      const messageWithUndefined = {
        eventType: 'USER_LOGIN',
        timestamp: '2025-06-26T12:00:00.000Z',
        username: 'testuser',
        method: 'POST',
        endpoint: '/api/login',
        body: { field: 'value' },
        ip: undefined,
        userAgent: undefined,
      };

      // Act & Assert
      await expect(service.handleMessage(messageWithUndefined)).rejects.toThrow(
        'Invalid AuditMessage: Missing required fields',
      );
    });

    it('should handle empty message object', async () => {
      // Arrange
      const emptyMessage = {};

      // Act & Assert
      await expect(service.handleMessage(emptyMessage)).rejects.toThrow(
        'Invalid AuditMessage: Missing required fields',
      );
    });

    it('should handle message with all required fields as empty strings', async () => {
      // Arrange
      const messageWithEmptyStrings = {
        eventType: '',
        timestamp: '',
        username: '',
        method: '',
        endpoint: '',
        body: {},
        ip: '',
        userAgent: '',
      };

      // Act & Assert
      await expect(
        service.handleMessage(messageWithEmptyStrings),
      ).rejects.toThrow('Invalid AuditMessage: Missing required fields');
    });

    it('should handle very large message payload', async () => {
      // Arrange
      const largeBody = {};
      for (let i = 0; i < 1000; i++) {
        largeBody[`field${i}`] = `value${i}`.repeat(100);
      }

      const largeMessage = {
        eventType: 'LARGE_EVENT',
        timestamp: '2025-06-26T12:00:00.000Z',
        username: 'testuser',
        method: 'POST',
        endpoint: '/api/large',
        body: largeBody,
        ip: '***********',
        userAgent: 'Mozilla/5.0 Test Agent',
      };

      mockAuditService.create.mockResolvedValue(undefined);

      // Act
      await service.handleMessage(largeMessage);

      // Assert
      expect(loggerSpy).toHaveBeenCalledWith(
        expect.stringContaining('Mensagem recebida:'),
      );
      // eslint-disable-next-line @typescript-eslint/unbound-method
      expect(auditService.create).toHaveBeenCalledTimes(1);
    });
  });
});

describe('RabbitmqService - Environment Variable Tests', () => {
  // This test suite needs to be separate to test environment variable initialization behavior
  let originalEnv: Record<string, string | undefined>;

  beforeEach(() => {
    // Store original environment variables
    originalEnv = {
      AUDIT_EXCHANGE_NAME: process.env.AUDIT_EXCHANGE_NAME,
      AUDIT_ROUTING_KEY: process.env.AUDIT_ROUTING_KEY,
      AUDIT_QUEUE_NAME: process.env.AUDIT_QUEUE_NAME,
    };
  });

  afterEach(() => {
    // Restore original environment variables
    process.env.AUDIT_EXCHANGE_NAME = originalEnv.AUDIT_EXCHANGE_NAME;
    process.env.AUDIT_ROUTING_KEY = originalEnv.AUDIT_ROUTING_KEY;
    process.env.AUDIT_QUEUE_NAME = originalEnv.AUDIT_QUEUE_NAME;
  });

  it('should throw error when AUDIT_EXCHANGE_NAME is not defined', () => {
    // Arrange
    delete process.env.AUDIT_EXCHANGE_NAME;
    process.env.AUDIT_ROUTING_KEY = 'test.routing.key';
    process.env.AUDIT_QUEUE_NAME = 'test.queue';

    // Act & Assert
    expect(() => {
      // We need to re-import the module to trigger the decorator evaluation
      jest.isolateModules(() => {
        // This will throw when the decorator is evaluated
        // eslint-disable-next-line @typescript-eslint/no-require-imports
        require('./rabbitmq.service');
      });
    }).toThrow('AUDIT_EXCHANGE_NAME is not defined');
  });

  it('should throw error when AUDIT_ROUTING_KEY is not defined', () => {
    // Arrange
    process.env.AUDIT_EXCHANGE_NAME = 'test.exchange';
    delete process.env.AUDIT_ROUTING_KEY;
    process.env.AUDIT_QUEUE_NAME = 'test.queue';

    // Act & Assert
    expect(() => {
      jest.isolateModules(() => {
        // This will throw when the decorator is evaluated
        // eslint-disable-next-line @typescript-eslint/no-require-imports
        require('./rabbitmq.service');
      });
    }).toThrow('AUDIT_ROUTING_KEY is not defined');
  });

  it('should throw error when AUDIT_QUEUE_NAME is not defined', () => {
    // Arrange
    process.env.AUDIT_EXCHANGE_NAME = 'test.exchange';
    process.env.AUDIT_ROUTING_KEY = 'test.routing.key';
    delete process.env.AUDIT_QUEUE_NAME;

    // Act & Assert
    expect(() => {
      jest.isolateModules(() => {
        // This will throw when the decorator is evaluated
        // eslint-disable-next-line @typescript-eslint/no-require-imports
        require('./rabbitmq.service');
      });
    }).toThrow('AUDIT_QUEUE_NAME is not defined');
  });

  it('should initialize successfully when all environment variables are defined', () => {
    // Arrange
    process.env.AUDIT_EXCHANGE_NAME = 'test.exchange';
    process.env.AUDIT_ROUTING_KEY = 'test.routing.key';
    process.env.AUDIT_QUEUE_NAME = 'test.queue';

    // Act & Assert
    expect(() => {
      jest.isolateModules(() => {
        // This should not throw when all environment variables are defined
        // eslint-disable-next-line @typescript-eslint/no-require-imports, @typescript-eslint/no-unsafe-assignment
        const { RabbitmqService } = require('./rabbitmq.service');
        expect(RabbitmqService).toBeDefined();
      });
    }).not.toThrow();
  });
});
