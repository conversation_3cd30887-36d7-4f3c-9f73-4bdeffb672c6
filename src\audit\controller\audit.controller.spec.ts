import { Test, TestingModule } from '@nestjs/testing';
import { AuditController } from './audit.controller';
import { AuditService } from '../service/audit.service';
import { AuditMessage } from '../domain/audit-message';

/* eslint-disable @typescript-eslint/unbound-method */

describe('AuditController', () => {
  let controller: AuditController;
  let auditService: AuditService;

  const mockAuditService = {
    findAll: jest.fn(),
    create: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AuditController],
      providers: [
        {
          provide: AuditService,
          useValue: mockAuditService,
        },
      ],
    }).compile();

    controller = module.get<AuditController>(AuditController);
    auditService = module.get<AuditService>(AuditService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('controller instance', () => {
    it('should be defined', () => {
      expect(controller).toBeDefined();
    });

    it('should have auditService injected', () => {
      expect(auditService).toBeDefined();
    });
  });

  describe('findAll', () => {
    const mockAuditMessage = new AuditMessage(
      'USER_LOGIN',
      '2025-06-29T10:00:00.000Z',
      'testuser',
      'POST',
      '/api/login',
      { email: '<EMAIL>' },
      '192.168.1.1',
      'Mozilla/5.0',
    );

    const mockResult = {
      data: [mockAuditMessage],
      total: 1,
      page: 1,
      limit: 10,
      totalPages: 1,
    };

    it('should call auditService.findAll with correct parameters', async () => {
      mockAuditService.findAll.mockResolvedValue(mockResult);

      const result = await controller.findAll('testuser', '2025-06-29', 1, 10);

      expect(auditService.findAll).toHaveBeenCalledWith(
        'testuser',
        '2025-06-29',
        1,
        10,
      );
      expect(result).toEqual(mockResult);
    });

    it('should handle optional username parameter', async () => {
      mockAuditService.findAll.mockResolvedValue(mockResult);

      await controller.findAll('', '2025-06-29', 1, 10);

      expect(auditService.findAll).toHaveBeenCalledWith(
        '',
        '2025-06-29',
        1,
        10,
      );
    });

    it('should handle optional timestamp parameter', async () => {
      mockAuditService.findAll.mockResolvedValue(mockResult);

      await controller.findAll('testuser', '', 1, 10);

      expect(auditService.findAll).toHaveBeenCalledWith('testuser', '', 1, 10);
    });

    it('should handle both optional parameters as empty strings', async () => {
      mockAuditService.findAll.mockResolvedValue(mockResult);

      await controller.findAll('', '', 1, 10);

      expect(auditService.findAll).toHaveBeenCalledWith('', '', 1, 10);
    });

    it('should pass the limit parameter to service (service handles max limit)', async () => {
      mockAuditService.findAll.mockResolvedValue(mockResult);

      await controller.findAll('testuser', '2025-06-29', 1, 100);

      expect(auditService.findAll).toHaveBeenCalledWith(
        'testuser',
        '2025-06-29',
        1,
        100, // Should NOT be limited to 50 here, service handles it
      );
    });

    it('should preserve limit when it is within the allowed range', async () => {
      mockAuditService.findAll.mockResolvedValue(mockResult);

      await controller.findAll('testuser', '2025-06-29', 1, 25);

      expect(auditService.findAll).toHaveBeenCalledWith(
        'testuser',
        '2025-06-29',
        1,
        25, // Should remain 25
      );
    });

    it('should handle different page numbers', async () => {
      mockAuditService.findAll.mockResolvedValue({
        ...mockResult,
        page: 2,
      });

      await controller.findAll('testuser', '2025-06-29', 2, 10);

      expect(auditService.findAll).toHaveBeenCalledWith(
        'testuser',
        '2025-06-29',
        2,
        10,
      );
    });

    it('should return empty results when no data found', async () => {
      const emptyResult = {
        data: [],
        total: 0,
        page: 1,
        limit: 10,
        totalPages: 0,
      };
      mockAuditService.findAll.mockResolvedValue(emptyResult);

      const result = await controller.findAll(
        'nonexistent',
        '2025-06-29',
        1,
        10,
      );

      expect(result).toEqual(emptyResult);
      expect(auditService.findAll).toHaveBeenCalledWith(
        'nonexistent',
        '2025-06-29',
        1,
        10,
      );
    });

    it('should handle multiple audit records', async () => {
      const multipleRecordsResult = {
        data: [mockAuditMessage, mockAuditMessage],
        total: 25,
        page: 2,
        limit: 10,
        totalPages: 3,
      };
      mockAuditService.findAll.mockResolvedValue(multipleRecordsResult);

      const result = await controller.findAll('testuser', '2025-06-29', 2, 10);

      expect(result).toEqual(multipleRecordsResult);
      expect(result.data).toHaveLength(2);
    });

    it('should propagate service errors', async () => {
      const error = new Error('Database connection failed');
      mockAuditService.findAll.mockRejectedValue(error);

      await expect(
        controller.findAll('testuser', '2025-06-29', 1, 10),
      ).rejects.toThrow('Database connection failed');
    });

    it('should handle edge case with limit of 1', async () => {
      mockAuditService.findAll.mockResolvedValue({
        ...mockResult,
        limit: 1,
      });

      await controller.findAll('testuser', '2025-06-29', 1, 1);

      expect(auditService.findAll).toHaveBeenCalledWith(
        'testuser',
        '2025-06-29',
        1,
        1,
      );
    });

    it('should handle edge case with limit of 50 (boundary)', async () => {
      mockAuditService.findAll.mockResolvedValue({
        ...mockResult,
        limit: 50,
      });

      await controller.findAll('testuser', '2025-06-29', 1, 50);

      expect(auditService.findAll).toHaveBeenCalledWith(
        'testuser',
        '2025-06-29',
        1,
        50,
      );
    });
  });
});
