import { PrismaService } from '../../prisma/prisma.service';
import { AuditMessage } from '../domain/audit-message';
import {
  AuditRepository,
  FindAllParams,
  FindAllResult,
} from './audit.repository.interface';
import { Injectable } from '@nestjs/common';
import { Prisma } from '@prisma/client';

@Injectable()
export class PrismaAuditRepository implements AuditRepository {
  constructor(private readonly prismaService: PrismaService) {}

  async create(auditMessage: AuditMessage): Promise<void> {
    await this.prismaService.audit.create({
      data: {
        eventType: auditMessage.eventType,
        timestamp: auditMessage.timestamp,
        username: auditMessage.username,
        method: auditMessage.method,
        endpoint: auditMessage.endpoint,
        body: auditMessage.body as Prisma.InputJsonValue,
        ip: auditMessage.ip,
        userAgent: auditMessage.userAgent,
      },
    });
  }

  async findAll(params: FindAllParams): Promise<FindAllResult> {
    const { username, timestamp, page, limit, offset } = params;

    // Build where clause based on provided parameters
    const where: Prisma.AuditWhereInput = {};

    if (username) {
      where.username = {
        contains: username,
        mode: 'insensitive',
      };
    }

    if (timestamp) {
      // Parse timestamp and create date range for the day
      const date = new Date(timestamp);
      const startOfDay = new Date(
        date.getFullYear(),
        date.getMonth(),
        date.getDate(),
      );
      const endOfDay = new Date(
        date.getFullYear(),
        date.getMonth(),
        date.getDate() + 1,
      );

      where.timestamp = {
        gte: startOfDay,
        lt: endOfDay,
      };
    }

    // Get total count for pagination
    const total = await this.prismaService.audit.count({ where });

    // Get paginated results
    const audits = await this.prismaService.audit.findMany({
      where,
      skip: offset,
      take: limit,
      orderBy: {
        timestamp: 'desc',
      },
    });

    // Convert Prisma results to AuditMessage domain objects
    const data = audits.map(
      (audit) =>
        new AuditMessage(
          audit.eventType,
          audit.timestamp.toISOString(),
          audit.username,
          audit.method,
          audit.endpoint,
          audit.body as Record<string, unknown>,
          audit.ip || 'unknown',
          audit.userAgent || 'unknown',
        ),
    );

    const totalPages = Math.ceil(total / limit);

    return {
      data,
      total,
      page,
      limit,
      totalPages,
    };
  }
}
