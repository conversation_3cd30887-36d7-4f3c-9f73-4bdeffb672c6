******************************************************************
*** PLEASE BE PATIENT: Parse may take a few minutes to install ***
******************************************************************

Thank you for installing helm chart name: {{ .Chart.Name }} and version: {{ .Chart.Version }}.

Your is Helm name: {{ .Release.Name }}.

To learn more about the release, try:

1. Get the Application by running this command:
    kubectl get deployment {{.Values.name }} -n {{ .Release.Namespace }}

2. Get the Service by running this command:
    kubectl get service {{.Values.name }} -n {{ .Release.Namespace }}

3. Get the ConfigMap by running this command:
    kubectl get configmap {{.Values.name }} -n {{ .Release.Namespace }}

4. Get the Secrets by running this command:
    kubectl get secrets {{.Values.name }} -n {{ .Release.Namespace }}

5. Get the Pods by running this command:
    kubectl get po {{.Values.name }} -n {{ .Release.Namespace }}

6. Get the HPA by running this command:
    kubectl get hpa {{.Values.name }} -n {{ .Release.Namespace }}

7. Get the Ingress by running this command:
    kubectl get ingress {{.Values.name }} -n {{ .Release.Namespace }}

8. List all kubernetes objects with specific labels ex. command:
    kubectl get all -l app={{ .Values.name }}