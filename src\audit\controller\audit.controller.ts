import {
  Controller,
  Get,
  Query,
  ParseIntPipe,
  DefaultValuePipe,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiQuery, ApiResponse } from '@nestjs/swagger';
import { AuditService } from '../service/audit.service';

@ApiTags('Logs')
@Controller('audit')
export class AuditController {
  constructor(private readonly auditService: AuditService) {}

  @Get('/logs')
  @ApiOperation({ summary: 'Get audit logs with pagination and filtering' })
  @ApiQuery({
    name: 'username',
    required: false,
    description: 'Filter by username',
  })
  @ApiQuery({
    name: 'timestamp',
    required: false,
    description: 'Filter by timestamp',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'Page number (default: 1)',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'Records per page (default: 10, max: 50)',
  })
  @ApiResponse({ status: 200, description: 'Returns paginated audit logs' })
  findAll(
    @Query('username') username: string,
    @Query('timestamp') timestamp: string,
    @Query('page', new DefaultValuePipe(1), ParseIntPipe) page: number,
    @Query('limit', new DefaultValuePipe(10), ParseIntPipe) limit: number,
  ) {
    return this.auditService.findAll(username, timestamp, page, limit);
  }
}
