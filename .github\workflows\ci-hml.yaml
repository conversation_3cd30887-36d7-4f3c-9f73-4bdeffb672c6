name: ci-hml

# on:
#   push:
#     branches:
#       - homolog

on:
  pull_request:
    types: [opened, synchronize, reopened, labeled]

env:
  # AWS Configuration
  AWS_REGION: us-east-2
  EKS_CLUSTER_NAME_HML: hml

jobs:
  development:
    runs-on: ubuntu-latest
    if: ${{ github.event.pull_request.labels && contains(join(github.event.pull_request.labels.*.name, ','), 'ci-hml') }}
    steps:
      - name: Set short git commit SHA
        id: commit
        uses: prompt/actions-commit-hash@v2

      - name: Checkout do repositório
        uses: actions/checkout@v3

      - name: Instalar kubectl
        uses: azure/setup-kubectl@v2.0
        with:
          version: "v1.24.0"

      - name: Configurar credenciais AWS
        uses: aws-actions/configure-aws-credentials@v1
        with:
          aws-access-key-id:     ${{ secrets.AWS_ACCESS_KEY_ID_CI }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY_CI }}
          aws-region:            ${{ env.AWS_REGION }}

      - name: Login no ECR
        run: |
          aws ecr get-login-password --region ${{ env.AWS_REGION }} \
            | docker login --username AWS --password-stdin 267316525040.dkr.ecr.${{ env.AWS_REGION }}.amazonaws.com

      - name: Build & Push da imagem
        env:
          IMAGE_TAG: ${{ steps.commit.outputs.short }}
        run: |
          docker build -t backoffice-audit-service:$IMAGE_TAG --target development .
          docker tag backoffice-audit-service:$IMAGE_TAG 267316525040.dkr.ecr.${{ env.AWS_REGION }}.amazonaws.com/backoffice-audit-service:$IMAGE_TAG
          docker push 267316525040.dkr.ecr.${{ env.AWS_REGION }}.amazonaws.com/backoffice-audit-service:$IMAGE_TAG

      - name: Configurar cliente Kubernetes
        uses: silverlyra/setup-aws-eks@v0.1.1
        with:
          cluster: ${{ env.EKS_CLUSTER_NAME_HML }}

      - name: Instalar Helm
        run: curl https://raw.githubusercontent.com/helm/helm/main/scripts/get-helm-3 | bash

      - name: Deploy com Helm
        run: |
          cd helm/hml/backoffice-audit-service-hml/
          sed -i.bak "s|latest|${{ steps.commit.outputs.short }}|g" values.yaml
          helm upgrade backoffice-audit-service-hml . -f values.yaml -n backoffice-hml -i --force
